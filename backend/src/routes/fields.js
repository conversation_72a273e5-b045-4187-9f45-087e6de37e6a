const express = require('express');
const router = express.Router();

// 支持的字段类型配置
const fieldTypes = [
  {
    type: 'input',
    name: '单行文本',
    icon: 'text',
    defaultProps: {
      label: '单行文本',
      placeholder: '请输入',
      required: false,
      maxLength: 100
    }
  },
  {
    type: 'textarea',
    name: '多行文本',
    icon: 'textarea',
    defaultProps: {
      label: '多行文本',
      placeholder: '请输入',
      required: false,
      rows: 4,
      maxLength: 500
    }
  },
  {
    type: 'number',
    name: '数字',
    icon: 'number',
    defaultProps: {
      label: '数字',
      placeholder: '请输入数字',
      required: false,
      min: null,
      max: null
    }
  },
  {
    type: 'select',
    name: '下拉选择',
    icon: 'select',
    defaultProps: {
      label: '下拉选择',
      placeholder: '请选择',
      required: false,
      options: [
        { label: '选项1', value: 'option1' },
        { label: '选项2', value: 'option2' }
      ]
    }
  },
  {
    type: 'radio',
    name: '单选框',
    icon: 'radio',
    defaultProps: {
      label: '单选框',
      required: false,
      options: [
        { label: '选项1', value: 'option1' },
        { label: '选项2', value: 'option2' }
      ]
    }
  },
  {
    type: 'checkbox',
    name: '多选框',
    icon: 'checkbox',
    defaultProps: {
      label: '多选框',
      required: false,
      options: [
        { label: '选项1', value: 'option1' },
        { label: '选项2', value: 'option2' }
      ]
    }
  },
  {
    type: 'date',
    name: '日期',
    icon: 'calendar',
    defaultProps: {
      label: '日期',
      placeholder: '请选择日期',
      required: false,
      format: 'YYYY-MM-DD'
    }
  },
  {
    type: 'time',
    name: '时间',
    icon: 'clock',
    defaultProps: {
      label: '时间',
      placeholder: '请选择时间',
      required: false,
      format: 'HH:mm'
    }
  },
  {
    type: 'email',
    name: '邮箱',
    icon: 'mail',
    defaultProps: {
      label: '邮箱',
      placeholder: '请输入邮箱地址',
      required: false
    }
  },
  {
    type: 'phone',
    name: '手机号',
    icon: 'phone',
    defaultProps: {
      label: '手机号',
      placeholder: '请输入手机号',
      required: false
    }
  }
];

// 获取所有字段类型
router.get('/types', (req, res) => {
  res.json({
    success: true,
    data: fieldTypes
  });
});

// 获取特定字段类型的默认配置
router.get('/types/:type', (req, res) => {
  const fieldType = fieldTypes.find(f => f.type === req.params.type);
  if (!fieldType) {
    return res.status(404).json({
      success: false,
      message: '字段类型不存在'
    });
  }
  res.json({
    success: true,
    data: fieldType
  });
});

module.exports = router;
