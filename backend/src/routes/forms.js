const express = require('express');
const Form = require('../models/Form');
const { generateMockForms } = require('../utils/mockData');

const router = express.Router();

// 初始化一些mock数据
generateMockForms();

// 获取所有表单
router.get('/', (req, res) => {
  try {
    const forms = Form.getAll();
    res.json({
      success: true,
      data: forms,
      total: forms.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取表单列表失败',
      error: error.message
    });
  }
});

// 根据ID获取表单
router.get('/:id', (req, res) => {
  try {
    const form = Form.getById(req.params.id);
    if (!form) {
      return res.status(404).json({
        success: false,
        message: '表单不存在'
      });
    }
    res.json({
      success: true,
      data: form
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取表单失败',
      error: error.message
    });
  }
});

// 创建新表单
router.post('/', (req, res) => {
  try {
    const form = new Form(req.body);
    form.save();
    res.status(201).json({
      success: true,
      data: form,
      message: '表单创建成功'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '创建表单失败',
      error: error.message
    });
  }
});

// 更新表单
router.put('/:id', (req, res) => {
  try {
    const existingForm = Form.getById(req.params.id);
    if (!existingForm) {
      return res.status(404).json({
        success: false,
        message: '表单不存在'
      });
    }
    
    const updatedForm = new Form({
      ...existingForm,
      ...req.body,
      id: req.params.id,
      updatedAt: new Date().toISOString()
    });
    updatedForm.save();
    
    res.json({
      success: true,
      data: updatedForm,
      message: '表单更新成功'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '更新表单失败',
      error: error.message
    });
  }
});

// 删除表单
router.delete('/:id', (req, res) => {
  try {
    const deleted = Form.deleteById(req.params.id);
    if (!deleted) {
      return res.status(404).json({
        success: false,
        message: '表单不存在'
      });
    }
    res.json({
      success: true,
      message: '表单删除成功'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '删除表单失败',
      error: error.message
    });
  }
});

module.exports = router;
