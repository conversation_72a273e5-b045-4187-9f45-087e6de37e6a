const express = require('express');
const Form = require('../models/Form');

const router = express.Router();

// 提交表单数据
router.post('/:formId', (req, res) => {
  try {
    const { formId } = req.params;
    const formData = req.body;

    // 验证表单是否存在
    const form = Form.getById(formId);
    if (!form) {
      return res.status(404).json({
        success: false,
        message: '表单不存在'
      });
    }

    // 简单的数据验证
    const errors = validateFormData(form, formData);
    if (errors.length > 0) {
      return res.status(400).json({
        success: false,
        message: '数据验证失败',
        errors
      });
    }

    // 保存数据
    const submission = Form.saveFormData(formId, formData);
    
    res.status(201).json({
      success: true,
      data: submission,
      message: '数据提交成功'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '提交数据失败',
      error: error.message
    });
  }
});

// 获取表单提交数据
router.get('/:formId', (req, res) => {
  try {
    const { formId } = req.params;
    
    // 验证表单是否存在
    const form = Form.getById(formId);
    if (!form) {
      return res.status(404).json({
        success: false,
        message: '表单不存在'
      });
    }

    const submissions = Form.getFormData(formId);
    
    res.json({
      success: true,
      data: submissions,
      total: submissions.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取数据失败',
      error: error.message
    });
  }
});

// 简单的表单数据验证函数
function validateFormData(form, data) {
  const errors = [];
  
  form.fields.forEach(field => {
    const value = data[field.key];
    
    // 必填验证
    if (field.required && (!value || value.toString().trim() === '')) {
      errors.push({
        field: field.key,
        message: `${field.label} 为必填项`
      });
    }
    
    // 邮箱格式验证
    if (field.type === 'email' && value) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        errors.push({
          field: field.key,
          message: `${field.label} 格式不正确`
        });
      }
    }
    
    // 手机号格式验证
    if (field.type === 'phone' && value) {
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(value)) {
        errors.push({
          field: field.key,
          message: `${field.label} 格式不正确`
        });
      }
    }
    
    // 数字范围验证
    if (field.type === 'number' && value !== null && value !== undefined) {
      const numValue = Number(value);
      if (field.min !== null && numValue < field.min) {
        errors.push({
          field: field.key,
          message: `${field.label} 不能小于 ${field.min}`
        });
      }
      if (field.max !== null && numValue > field.max) {
        errors.push({
          field: field.key,
          message: `${field.label} 不能大于 ${field.max}`
        });
      }
    }
  });
  
  return errors;
}

module.exports = router;
