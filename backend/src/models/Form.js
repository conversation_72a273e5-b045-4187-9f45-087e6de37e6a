const { v4: uuidv4 } = require('uuid');

// 内存存储 (生产环境应使用数据库)
let forms = [];
let formData = {};

class Form {
  constructor(data) {
    this.id = data.id || uuidv4();
    this.name = data.name || '';
    this.description = data.description || '';
    this.fields = data.fields || [];
    this.settings = data.settings || {
      submitText: '提交',
      resetText: '重置',
      layout: 'vertical'
    };
    this.createdAt = data.createdAt || new Date().toISOString();
    this.updatedAt = new Date().toISOString();
    this.status = data.status || 'draft'; // draft, published, archived
  }

  // 保存表单
  save() {
    const index = forms.findIndex(f => f.id === this.id);
    if (index >= 0) {
      forms[index] = this;
    } else {
      forms.push(this);
    }
    return this;
  }

  // 获取所有表单
  static getAll() {
    return forms;
  }

  // 根据ID获取表单
  static getById(id) {
    return forms.find(f => f.id === id);
  }

  // 删除表单
  static deleteById(id) {
    const index = forms.findIndex(f => f.id === id);
    if (index >= 0) {
      forms.splice(index, 1);
      // 同时删除相关的表单数据
      delete formData[id];
      return true;
    }
    return false;
  }

  // 保存表单提交数据
  static saveFormData(formId, data) {
    if (!formData[formId]) {
      formData[formId] = [];
    }
    const submission = {
      id: uuidv4(),
      formId,
      data,
      submittedAt: new Date().toISOString()
    };
    formData[formId].push(submission);
    return submission;
  }

  // 获取表单提交数据
  static getFormData(formId) {
    return formData[formId] || [];
  }
}

module.exports = Form;
