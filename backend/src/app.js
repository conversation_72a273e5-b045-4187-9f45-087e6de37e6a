const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');

const formRoutes = require('./routes/forms');
const fieldRoutes = require('./routes/fields');
const dataRoutes = require('./routes/data');

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// 路由
app.use('/api/forms', formRoutes);
app.use('/api/fields', fieldRoutes);
app.use('/api/data', dataRoutes);

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', message: '轻搭后端服务运行正常' });
});

// 错误处理
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: '服务器内部错误' });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({ error: '接口不存在' });
});

app.listen(PORT, () => {
  console.log(`轻搭后端服务启动成功，端口: ${PORT}`);
  console.log(`API文档: http://localhost:${PORT}/api/health`);
});

module.exports = app;
