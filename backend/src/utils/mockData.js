const Form = require('../models/Form');

// 生成mock表单数据
function generateMockForms() {
  // 用户注册表单
  const userRegistrationForm = new Form({
    name: '用户注册表单',
    description: '新用户注册信息收集表单',
    status: 'published',
    fields: [
      {
        key: 'username',
        type: 'input',
        label: '用户名',
        placeholder: '请输入用户名',
        required: true,
        maxLength: 20
      },
      {
        key: 'email',
        type: 'email',
        label: '邮箱地址',
        placeholder: '请输入邮箱地址',
        required: true
      },
      {
        key: 'phone',
        type: 'phone',
        label: '手机号码',
        placeholder: '请输入手机号码',
        required: true
      },
      {
        key: 'gender',
        type: 'radio',
        label: '性别',
        required: false,
        options: [
          { label: '男', value: 'male' },
          { label: '女', value: 'female' },
          { label: '其他', value: 'other' }
        ]
      },
      {
        key: 'birthday',
        type: 'date',
        label: '出生日期',
        placeholder: '请选择出生日期',
        required: false
      },
      {
        key: 'interests',
        type: 'checkbox',
        label: '兴趣爱好',
        required: false,
        options: [
          { label: '运动', value: 'sports' },
          { label: '音乐', value: 'music' },
          { label: '阅读', value: 'reading' },
          { label: '旅行', value: 'travel' },
          { label: '摄影', value: 'photography' }
        ]
      },
      {
        key: 'bio',
        type: 'textarea',
        label: '个人简介',
        placeholder: '请简单介绍一下自己',
        required: false,
        rows: 4,
        maxLength: 200
      }
    ],
    settings: {
      submitText: '注册',
      resetText: '重置',
      layout: 'vertical'
    }
  });

  // 产品反馈表单
  const feedbackForm = new Form({
    name: '产品反馈表单',
    description: '收集用户对产品的意见和建议',
    status: 'published',
    fields: [
      {
        key: 'product',
        type: 'select',
        label: '产品名称',
        placeholder: '请选择产品',
        required: true,
        options: [
          { label: '轻搭表单平台', value: 'qingda' },
          { label: '其他产品A', value: 'productA' },
          { label: '其他产品B', value: 'productB' }
        ]
      },
      {
        key: 'rating',
        type: 'radio',
        label: '整体评分',
        required: true,
        options: [
          { label: '⭐ 1分', value: '1' },
          { label: '⭐⭐ 2分', value: '2' },
          { label: '⭐⭐⭐ 3分', value: '3' },
          { label: '⭐⭐⭐⭐ 4分', value: '4' },
          { label: '⭐⭐⭐⭐⭐ 5分', value: '5' }
        ]
      },
      {
        key: 'features',
        type: 'checkbox',
        label: '喜欢的功能',
        required: false,
        options: [
          { label: '界面设计', value: 'ui' },
          { label: '操作简单', value: 'easy' },
          { label: '功能丰富', value: 'features' },
          { label: '响应速度', value: 'speed' },
          { label: '稳定性', value: 'stable' }
        ]
      },
      {
        key: 'suggestions',
        type: 'textarea',
        label: '改进建议',
        placeholder: '请提出您的宝贵建议',
        required: false,
        rows: 5,
        maxLength: 500
      },
      {
        key: 'contact',
        type: 'email',
        label: '联系邮箱',
        placeholder: '如需回复请留下邮箱',
        required: false
      }
    ]
  });

  // 活动报名表单
  const eventForm = new Form({
    name: '技术分享会报名',
    description: '2024年前端技术分享会报名表单',
    status: 'draft',
    fields: [
      {
        key: 'name',
        type: 'input',
        label: '姓名',
        placeholder: '请输入真实姓名',
        required: true,
        maxLength: 10
      },
      {
        key: 'company',
        type: 'input',
        label: '公司/组织',
        placeholder: '请输入公司或组织名称',
        required: false,
        maxLength: 50
      },
      {
        key: 'position',
        type: 'input',
        label: '职位',
        placeholder: '请输入职位',
        required: false,
        maxLength: 30
      },
      {
        key: 'experience',
        type: 'select',
        label: '工作经验',
        placeholder: '请选择工作经验',
        required: true,
        options: [
          { label: '1年以下', value: '0-1' },
          { label: '1-3年', value: '1-3' },
          { label: '3-5年', value: '3-5' },
          { label: '5年以上', value: '5+' }
        ]
      },
      {
        key: 'topics',
        type: 'checkbox',
        label: '感兴趣的话题',
        required: true,
        options: [
          { label: 'React 18新特性', value: 'react18' },
          { label: 'Vue 3组合式API', value: 'vue3' },
          { label: '微前端架构', value: 'microfrontend' },
          { label: '性能优化', value: 'performance' },
          { label: 'TypeScript实践', value: 'typescript' }
        ]
      }
    ]
  });

  // 保存mock数据
  userRegistrationForm.save();
  feedbackForm.save();
  eventForm.save();

  console.log('Mock表单数据已生成');
}

module.exports = {
  generateMockForms
};
