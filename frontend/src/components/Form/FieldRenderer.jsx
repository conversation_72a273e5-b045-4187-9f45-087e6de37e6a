import React from 'react'
import {
  Input,
  InputNumber,
  Select,
  Radio,
  Checkbox,
  DatePicker,
  TimePicker,
  Form
} from 'antd'

const { TextArea } = Input
const { Option } = Select

function FieldRenderer({ field, value, onChange, preview = false }) {
  const { type, label, placeholder, required, options, ...props } = field

  // 表单项的通用配置
  const formItemProps = {
    label,
    required,
    name: field.key,
    rules: required ? [{ required: true, message: `请输入${label}` }] : []
  }

  // 渲染不同类型的字段
  const renderField = () => {
    switch (type) {
      case 'input':
        return (
          <Input
            placeholder={placeholder}
            value={value}
            onChange={(e) => onChange && onChange(e.target.value)}
            maxLength={props.maxLength}
            disabled={preview}
          />
        )

      case 'textarea':
        return (
          <TextArea
            placeholder={placeholder}
            value={value}
            onChange={(e) => onChange && onChange(e.target.value)}
            rows={props.rows || 4}
            maxLength={props.maxLength}
            disabled={preview}
          />
        )

      case 'number':
        return (
          <InputNumber
            placeholder={placeholder}
            value={value}
            onChange={(val) => onChange && onChange(val)}
            min={props.min}
            max={props.max}
            style={{ width: '100%' }}
            disabled={preview}
          />
        )

      case 'select':
        return (
          <Select
            placeholder={placeholder}
            value={value}
            onChange={(val) => onChange && onChange(val)}
            disabled={preview}
          >
            {options?.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        )

      case 'radio':
        return (
          <Radio.Group
            value={value}
            onChange={(e) => onChange && onChange(e.target.value)}
            disabled={preview}
          >
            {options?.map(option => (
              <Radio key={option.value} value={option.value}>
                {option.label}
              </Radio>
            ))}
          </Radio.Group>
        )

      case 'checkbox':
        return (
          <Checkbox.Group
            value={value}
            onChange={(val) => onChange && onChange(val)}
            disabled={preview}
          >
            {options?.map(option => (
              <Checkbox key={option.value} value={option.value}>
                {option.label}
              </Checkbox>
            ))}
          </Checkbox.Group>
        )

      case 'date':
        return (
          <DatePicker
            placeholder={placeholder}
            value={value}
            onChange={(date) => onChange && onChange(date)}
            format={props.format || 'YYYY-MM-DD'}
            style={{ width: '100%' }}
            disabled={preview}
          />
        )

      case 'time':
        return (
          <TimePicker
            placeholder={placeholder}
            value={value}
            onChange={(time) => onChange && onChange(time)}
            format={props.format || 'HH:mm'}
            style={{ width: '100%' }}
            disabled={preview}
          />
        )

      case 'email':
        return (
          <Input
            type="email"
            placeholder={placeholder}
            value={value}
            onChange={(e) => onChange && onChange(e.target.value)}
            disabled={preview}
          />
        )

      case 'phone':
        return (
          <Input
            placeholder={placeholder}
            value={value}
            onChange={(e) => onChange && onChange(e.target.value)}
            disabled={preview}
          />
        )

      default:
        return (
          <Input
            placeholder={placeholder}
            value={value}
            onChange={(e) => onChange && onChange(e.target.value)}
            disabled={preview}
          />
        )
    }
  }

  // 如果是预览模式，直接返回字段，不包装Form.Item
  if (preview) {
    return (
      <Form.Item {...formItemProps} style={{ marginBottom: 16 }}>
        {renderField()}
      </Form.Item>
    )
  }

  // 正常模式，返回可交互的字段
  return (
    <Form.Item {...formItemProps}>
      {renderField()}
    </Form.Item>
  )
}

export default FieldRenderer
