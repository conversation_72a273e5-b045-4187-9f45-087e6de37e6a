import React from 'react'
import { Layout, Button, Space } from 'antd'
import { useNavigate, useLocation } from 'react-router-dom'
import { 
  FormOutlined, 
  PlusOutlined, 
  HomeOutlined,
  GithubOutlined 
} from '@ant-design/icons'

const { Header: AntHeader } = Layout

function Header() {
  const navigate = useNavigate()
  const location = useLocation()

  const handleCreateForm = () => {
    navigate('/designer')
  }

  const handleGoHome = () => {
    navigate('/')
  }

  return (
    <AntHeader className="app-header">
      <div className="app-logo" onClick={handleGoHome} style={{ cursor: 'pointer' }}>
        <FormOutlined style={{ marginRight: 8, fontSize: 24 }} />
        轻搭
      </div>
      
      <Space>
        {location.pathname !== '/' && (
          <Button 
            icon={<HomeOutlined />} 
            onClick={handleGoHome}
          >
            首页
          </Button>
        )}
        
        <Button 
          type="primary" 
          icon={<PlusOutlined />}
          onClick={handleCreateForm}
        >
          创建表单
        </Button>
        
        <Button 
          icon={<GithubOutlined />}
          href="https://github.com"
          target="_blank"
        >
          GitHub
        </Button>
      </Space>
    </AntHeader>
  )
}

export default Header
