import React from 'react'
import { Empty, Typography } from 'antd'
import { useDrop } from 'react-dnd'
import { PlusOutlined } from '@ant-design/icons'

import FieldRenderer from '../Form/FieldRenderer'
import DraggableField from './DraggableField'

const { Text } = Typography

function DesignCanvas({ 
  fields, 
  selectedField, 
  onSelectField, 
  onUpdateField, 
  onDeleteField, 
  onMoveField,
  onAddField 
}) {
  const [{ isOver }, drop] = useDrop({
    accept: 'FIELD_TYPE',
    drop: (item) => {
      onAddField(item.fieldType)
    },
    collect: (monitor) => ({
      isOver: monitor.isOver()
    })
  })

  const handleFieldClick = (field) => {
    onSelectField(field)
  }

  const renderEmptyState = () => (
    <div 
      className={`drop-zone ${isOver ? 'drag-over' : ''}`}
      style={{ 
        minHeight: 400,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center'
      }}
    >
      <Empty
        image={<PlusOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />}
        description={
          <div>
            <Text type="secondary">
              {isOver ? '松开鼠标添加字段' : '拖拽左侧字段到此处开始设计表单'}
            </Text>
          </div>
        }
      />
    </div>
  )

  const renderFields = () => (
    <div style={{ padding: 16 }}>
      {fields.map((field, index) => (
        <DraggableField
          key={field.id}
          field={field}
          index={index}
          isSelected={selectedField && selectedField.id === field.id}
          onClick={() => handleFieldClick(field)}
          onUpdate={onUpdateField}
          onDelete={onDeleteField}
          onMove={onMoveField}
        >
          <FieldRenderer
            field={field}
            value=""
            onChange={() => {}}
            preview={true}
          />
        </DraggableField>
      ))}
      
      {/* 底部拖拽区域 */}
      <div 
        className={`drop-zone ${isOver ? 'drag-over' : ''}`}
        style={{ 
          minHeight: 60,
          marginTop: 16,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
      >
        <Text type="secondary" style={{ fontSize: 12 }}>
          {isOver ? '松开鼠标添加字段' : '拖拽字段到此处'}
        </Text>
      </div>
    </div>
  )

  return (
    <div 
      ref={drop}
      className="designer-preview"
      style={{ 
        background: isOver ? 'rgba(24, 144, 255, 0.02)' : '#f8f9fa'
      }}
    >
      {fields.length === 0 ? renderEmptyState() : renderFields()}
    </div>
  )
}

export default DesignCanvas
