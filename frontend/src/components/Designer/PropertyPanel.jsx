import React from 'react'
import { 
  Card, 
  Form, 
  Input, 
  Switch, 
  InputNumber, 
  Button, 
  Space,
  Typography,
  Divider
} from 'antd'
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons'

const { Title, Text } = Typography

function PropertyPanel({ selectedField, formData, onUpdateField, onUpdateForm }) {
  const [form] = Form.useForm()

  // 当选中字段变化时，更新表单值
  React.useEffect(() => {
    if (selectedField) {
      form.setFieldsValue(selectedField)
    }
  }, [selectedField, form])

  // 处理字段属性更新
  const handleFieldChange = (changedValues) => {
    if (selectedField) {
      onUpdateField(selectedField.id, changedValues)
    }
  }

  // 处理选项更新
  const handleOptionsChange = (options) => {
    if (selectedField) {
      onUpdateField(selectedField.id, { options })
    }
  }

  // 添加选项
  const addOption = () => {
    const options = selectedField?.options || []
    const newOption = {
      label: `选项${options.length + 1}`,
      value: `option${options.length + 1}`
    }
    handleOptionsChange([...options, newOption])
  }

  // 删除选项
  const removeOption = (index) => {
    const options = [...(selectedField?.options || [])]
    options.splice(index, 1)
    handleOptionsChange(options)
  }

  // 更新选项
  const updateOption = (index, field, value) => {
    const options = [...(selectedField?.options || [])]
    options[index] = { ...options[index], [field]: value }
    handleOptionsChange(options)
  }

  // 渲染选项编辑器
  const renderOptionsEditor = () => {
    if (!selectedField || !['select', 'radio', 'checkbox'].includes(selectedField.type)) {
      return null
    }

    const options = selectedField.options || []

    return (
      <Card size="small" title="选项设置" style={{ marginTop: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          {options.map((option, index) => (
            <div key={index} style={{ display: 'flex', gap: 8 }}>
              <Input
                placeholder="选项文字"
                value={option.label}
                onChange={(e) => updateOption(index, 'label', e.target.value)}
                style={{ flex: 1 }}
              />
              <Input
                placeholder="选项值"
                value={option.value}
                onChange={(e) => updateOption(index, 'value', e.target.value)}
                style={{ flex: 1 }}
              />
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                onClick={() => removeOption(index)}
              />
            </div>
          ))}
          
          <Button
            type="dashed"
            icon={<PlusOutlined />}
            onClick={addOption}
            style={{ width: '100%' }}
          >
            添加选项
          </Button>
        </Space>
      </Card>
    )
  }

  // 渲染数字字段特有属性
  const renderNumberProps = () => {
    if (!selectedField || selectedField.type !== 'number') {
      return null
    }

    return (
      <>
        <Form.Item label="最小值" name="min">
          <InputNumber style={{ width: '100%' }} />
        </Form.Item>
        <Form.Item label="最大值" name="max">
          <InputNumber style={{ width: '100%' }} />
        </Form.Item>
      </>
    )
  }

  // 渲染文本字段特有属性
  const renderTextProps = () => {
    if (!selectedField || !['input', 'textarea'].includes(selectedField.type)) {
      return null
    }

    return (
      <>
        <Form.Item label="最大长度" name="maxLength">
          <InputNumber min={1} style={{ width: '100%' }} />
        </Form.Item>
        {selectedField.type === 'textarea' && (
          <Form.Item label="行数" name="rows">
            <InputNumber min={1} max={10} style={{ width: '100%' }} />
          </Form.Item>
        )}
      </>
    )
  }

  if (!selectedField) {
    return (
      <div className="designer-properties">
        <div style={{ padding: 16, textAlign: 'center', color: '#999' }}>
          <Text type="secondary">请选择一个字段来编辑属性</Text>
        </div>
      </div>
    )
  }

  return (
    <div className="designer-properties">
      <div style={{ padding: 16 }}>
        <Title level={4} style={{ marginBottom: 16 }}>
          字段属性
        </Title>

        <Form
          form={form}
          layout="vertical"
          onValuesChange={handleFieldChange}
          size="small"
        >
          <Form.Item
            label="字段标识"
            name="key"
            rules={[{ required: true, message: '请输入字段标识' }]}
          >
            <Input placeholder="字段唯一标识" />
          </Form.Item>

          <Form.Item
            label="字段标题"
            name="label"
            rules={[{ required: true, message: '请输入字段标题' }]}
          >
            <Input placeholder="字段显示标题" />
          </Form.Item>

          <Form.Item label="占位符" name="placeholder">
            <Input placeholder="输入提示文字" />
          </Form.Item>

          <Form.Item label="是否必填" name="required" valuePropName="checked">
            <Switch />
          </Form.Item>

          {renderTextProps()}
          {renderNumberProps()}
        </Form>

        {renderOptionsEditor()}

        <Divider />

        <Text type="secondary" style={{ fontSize: 12 }}>
          字段类型: {selectedField.type}
        </Text>
      </div>
    </div>
  )
}

export default PropertyPanel
