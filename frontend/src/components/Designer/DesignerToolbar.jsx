import React, { useState } from 'react'
import {
  Button,
  Space,
  Input,
  Modal,
  Form,
  Select,
  Typography,
  Tag,
  Dropdown
} from 'antd'
import {
  SaveOutlined,
  EyeOutlined,
  SettingOutlined,
  ArrowLeftOutlined,
  DownOutlined
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'

const { Title } = Typography
const { Option } = Select

function DesignerToolbar({
  formData,
  loading,
  onSave,
  onPreview,
  onUpdateForm
}) {
  const navigate = useNavigate()
  const [settingsVisible, setSettingsVisible] = useState(false)
  const [form] = Form.useForm()

  // 状态配置
  const statusOptions = [
    { value: 'draft', label: '草稿', color: 'default' },
    { value: 'published', label: '已发布', color: 'success' },
    { value: 'archived', label: '已归档', color: 'warning' }
  ]

  const getCurrentStatus = () => {
    return statusOptions.find(option => option.value === formData.status) || statusOptions[0]
  }

  const handleNameChange = (e) => {
    onUpdateForm({ name: e.target.value })
  }

  const handleStatusChange = (status) => {
    onUpdateForm({ status })
  }

  const handleSettingsOk = () => {
    form.validateFields().then(values => {
      onUpdateForm(values)
      setSettingsVisible(false)
    })
  }

  const showSettings = () => {
    form.setFieldsValue(formData)
    setSettingsVisible(true)
  }

  return (
    <>
      <div className="designer-toolbar">
        <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/')}
          >
            返回
          </Button>

          <div style={{
            width: 1,
            height: 24,
            backgroundColor: '#e8e8e8',
            margin: '0 8px'
          }} />

          <Input
            value={formData.name}
            onChange={handleNameChange}
            placeholder="请输入表单名称"
            style={{
              width: 280,
              fontSize: 16,
              fontWeight: 500,
              border: 'none',
              boxShadow: 'none'
            }}
            size="large"
          />

          <Dropdown
            menu={{
              items: statusOptions.map(option => ({
                key: option.value,
                label: (
                  <div style={{ display: 'flex', alignItems: 'center', padding: '4px 0' }}>
                    <Tag
                      color={option.color}
                      style={{
                        margin: 0,
                        marginRight: 8,
                        fontSize: 12,
                        fontWeight: 500,
                        minWidth: 60,
                        textAlign: 'center'
                      }}
                    >
                      {option.label}
                    </Tag>
                  </div>
                ),
                onClick: () => handleStatusChange(option.value)
              }))
            }}
            trigger={['click']}
            placement="bottomLeft"
            overlayClassName="status-dropdown"
          >
            <Button
              className="status-selector"
              style={{
                border: '1px solid #d9d9d9',
                borderRadius: 6,
                height: 32,
                display: 'flex',
                alignItems: 'center',
                padding: '0 12px',
                background: '#fff'
              }}
            >
              <Tag
                color={getCurrentStatus().color}
                style={{
                  margin: 0,
                  marginRight: 6,
                  fontSize: 12,
                  fontWeight: 500,
                  border: 'none'
                }}
              >
                {getCurrentStatus().label}
              </Tag>
              <DownOutlined style={{ fontSize: 10, color: '#999' }} />
            </Button>
          </Dropdown>
        </div>

        <Space size={12}>
          <Button
            icon={<SettingOutlined />}
            onClick={showSettings}
            style={{ height: 32 }}
          >
            表单设置
          </Button>

          <Button
            icon={<EyeOutlined />}
            onClick={onPreview}
            style={{ height: 32 }}
          >
            预览
          </Button>

          <Button
            type="primary"
            icon={<SaveOutlined />}
            loading={loading}
            onClick={onSave}
            style={{ height: 32 }}
          >
            保存
          </Button>
        </Space>
      </div>

      {/* 表单设置弹窗 */}
      <Modal
        title="表单设置"
        open={settingsVisible}
        onOk={handleSettingsOk}
        onCancel={() => setSettingsVisible(false)}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={formData}
        >
          <Form.Item
            label="表单名称"
            name="name"
            rules={[{ required: true, message: '请输入表单名称' }]}
          >
            <Input placeholder="请输入表单名称" />
          </Form.Item>

          <Form.Item
            label="表单描述"
            name="description"
          >
            <Input.TextArea
              placeholder="请输入表单描述"
              rows={3}
            />
          </Form.Item>

          <Form.Item
            label="表单状态"
            name="status"
          >
            <Select placeholder="请选择表单状态">
              {statusOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  <Tag color={option.color} style={{ margin: 0 }}>
                    {option.label}
                  </Tag>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Title level={5}>按钮设置</Title>
          
          <Form.Item
            label="提交按钮文字"
            name={['settings', 'submitText']}
          >
            <Input placeholder="提交" />
          </Form.Item>

          <Form.Item
            label="重置按钮文字"
            name={['settings', 'resetText']}
          >
            <Input placeholder="重置" />
          </Form.Item>

          <Form.Item
            label="表单布局"
            name={['settings', 'layout']}
          >
            <Select>
              <Select.Option value="vertical">垂直布局</Select.Option>
              <Select.Option value="horizontal">水平布局</Select.Option>
              <Select.Option value="inline">内联布局</Select.Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </>
  )
}

export default DesignerToolbar
