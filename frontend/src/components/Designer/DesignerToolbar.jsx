import React, { useState } from 'react'
import {
  Button,
  Space,
  Input,
  Modal,
  Form,
  Select,
  Typography,
  Tag,
  Dropdown
} from 'antd'
import {
  SaveOutlined,
  EyeOutlined,
  SettingOutlined,
  ArrowLeftOutlined,
  DownOutlined
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'

const { Title } = Typography
const { Option } = Select

function DesignerToolbar({
  formData,
  loading,
  onSave,
  onPreview,
  onUpdateForm
}) {
  const navigate = useNavigate()
  const [settingsVisible, setSettingsVisible] = useState(false)
  const [form] = Form.useForm()

  // 状态配置
  const statusOptions = [
    { value: 'draft', label: '草稿', color: 'default' },
    { value: 'published', label: '已发布', color: 'success' },
    { value: 'archived', label: '已归档', color: 'warning' }
  ]

  const getCurrentStatus = () => {
    return statusOptions.find(option => option.value === formData.status) || statusOptions[0]
  }

  const handleNameChange = (e) => {
    onUpdateForm({ name: e.target.value })
  }

  const handleStatusChange = (status) => {
    onUpdateForm({ status })
  }

  const handleSettingsOk = () => {
    form.validateFields().then(values => {
      onUpdateForm(values)
      setSettingsVisible(false)
    })
  }

  const showSettings = () => {
    form.setFieldsValue(formData)
    setSettingsVisible(true)
  }

  return (
    <>
      <div className="designer-toolbar">
        <div style={{ display: 'flex', alignItems: 'center', flex: 1 }}>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/')}
            style={{
              marginRight: 16,
              height: 36,
              borderRadius: 8,
              fontWeight: 500,
              display: 'flex',
              alignItems: 'center',
              gap: 6
            }}
          >
            返回
          </Button>

          <div style={{
            width: 1,
            height: 28,
            backgroundColor: '#e8e8e8',
            margin: '0 20px'
          }} />

          <div style={{
            display: 'flex',
            alignItems: 'center',
            flex: 1,
            maxWidth: 400
          }}>
            <Input
              value={formData.name}
              onChange={handleNameChange}
              placeholder="请输入表单名称"
              style={{
                fontSize: 18,
                fontWeight: 600,
                border: 'none',
                boxShadow: 'none',
                background: 'transparent',
                padding: '8px 12px',
                color: '#262626'
              }}
              size="large"
            />
          </div>

          <Dropdown
            menu={{
              items: statusOptions.map(option => ({
                key: option.value,
                label: (
                  <div style={{ display: 'flex', alignItems: 'center', padding: '6px 12px' }}>
                    <Tag
                      color={option.color}
                      style={{
                        margin: 0,
                        fontSize: 12,
                        fontWeight: 500,
                        minWidth: 60,
                        textAlign: 'center',
                        borderRadius: 4
                      }}
                    >
                      {option.label}
                    </Tag>
                  </div>
                ),
                onClick: () => handleStatusChange(option.value)
              }))
            }}
            trigger={['click']}
            placement="bottomLeft"
            overlayClassName="status-dropdown"
          >
            <Button
              className="status-selector"
              style={{
                border: '1px solid #d9d9d9',
                borderRadius: 8,
                height: 36,
                display: 'flex',
                alignItems: 'center',
                padding: '0 14px',
                background: '#fff',
                boxShadow: '0 1px 2px rgba(0, 0, 0, 0.04)',
                marginRight: 32
              }}
            >
              <Tag
                color={getCurrentStatus().color}
                style={{
                  margin: 0,
                  marginRight: 8,
                  fontSize: 12,
                  fontWeight: 500,
                  border: 'none',
                  borderRadius: 4
                }}
              >
                {getCurrentStatus().label}
              </Tag>
              <DownOutlined style={{ fontSize: 10, color: '#999' }} />
            </Button>
          </Dropdown>
        </div>

        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <Button
            icon={<SettingOutlined />}
            onClick={showSettings}
            style={{
              height: 36,
              borderRadius: 8,
              fontWeight: 500,
              display: 'flex',
              alignItems: 'center',
              gap: 6,
              border: '1px solid #d9d9d9'
            }}
          >
            表单设置
          </Button>

          <Button
            icon={<EyeOutlined />}
            onClick={onPreview}
            style={{
              height: 36,
              borderRadius: 8,
              fontWeight: 500,
              display: 'flex',
              alignItems: 'center',
              gap: 6,
              border: '1px solid #d9d9d9'
            }}
          >
            预览
          </Button>

          <Button
            type="primary"
            icon={<SaveOutlined />}
            loading={loading}
            onClick={onSave}
            style={{
              height: 36,
              borderRadius: 8,
              fontWeight: 500,
              display: 'flex',
              alignItems: 'center',
              gap: 6,
              background: '#1890ff',
              borderColor: '#1890ff',
              boxShadow: '0 2px 4px rgba(24, 144, 255, 0.2)'
            }}
          >
            保存
          </Button>
        </div>
      </div>

      {/* 表单设置弹窗 */}
      <Modal
        title="表单设置"
        open={settingsVisible}
        onOk={handleSettingsOk}
        onCancel={() => setSettingsVisible(false)}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={formData}
        >
          <Form.Item
            label="表单名称"
            name="name"
            rules={[{ required: true, message: '请输入表单名称' }]}
          >
            <Input placeholder="请输入表单名称" />
          </Form.Item>

          <Form.Item
            label="表单描述"
            name="description"
          >
            <Input.TextArea
              placeholder="请输入表单描述"
              rows={3}
            />
          </Form.Item>

          <Form.Item
            label="表单状态"
            name="status"
          >
            <Select placeholder="请选择表单状态">
              {statusOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  <Tag color={option.color} style={{ margin: 0 }}>
                    {option.label}
                  </Tag>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Title level={5}>按钮设置</Title>
          
          <Form.Item
            label="提交按钮文字"
            name={['settings', 'submitText']}
          >
            <Input placeholder="提交" />
          </Form.Item>

          <Form.Item
            label="重置按钮文字"
            name={['settings', 'resetText']}
          >
            <Input placeholder="重置" />
          </Form.Item>

          <Form.Item
            label="表单布局"
            name={['settings', 'layout']}
          >
            <Select>
              <Select.Option value="vertical">垂直布局</Select.Option>
              <Select.Option value="horizontal">水平布局</Select.Option>
              <Select.Option value="inline">内联布局</Select.Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </>
  )
}

export default DesignerToolbar
