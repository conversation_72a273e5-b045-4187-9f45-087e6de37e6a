import React from 'react'
import { Card, Typography, Space } from 'antd'
import { useDrag } from 'react-dnd'
import {
  FontSizeOutlined,
  FileTextOutlined,
  NumberOutlined,
  CaretDownOutlined,
  CheckCircleOutlined,
  CheckSquareOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  MailOutlined,
  PhoneOutlined
} from '@ant-design/icons'

const { Title, Text } = Typography

// 字段类型图标映射
const fieldIcons = {
  input: FontSizeOutlined,
  textarea: FileTextOutlined,
  number: NumberOutlined,
  select: CaretDownOutlined,
  radio: CheckCircleOutlined,
  checkbox: CheckSquareOutlined,
  date: CalendarOutlined,
  time: ClockCircleOutlined,
  email: MailOutlined,
  phone: PhoneOutlined
}

// 可拖拽的字段项组件
function DraggableFieldItem({ fieldType, onAddField }) {
  const [{ isDragging }, drag] = useDrag({
    type: 'FIELD_TYPE',
    item: { fieldType },
    end: (item, monitor) => {
      if (monitor.didDrop()) {
        onAddField(item.fieldType)
      }
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging()
    })
  })

  const IconComponent = fieldIcons[fieldType.type] || FontSizeOutlined

  return (
    <div
      ref={drag}
      className="drag-item"
      style={{
        opacity: isDragging ? 0.5 : 1,
        cursor: 'move'
      }}
    >
      <Space>
        <IconComponent style={{ color: '#1890ff' }} />
        <Text>{fieldType.name}</Text>
      </Space>
    </div>
  )
}

function FieldSidebar({ fieldTypes, onAddField }) {
  // 基础字段
  const basicFields = fieldTypes.filter(field => 
    ['input', 'textarea', 'number'].includes(field.type)
  )
  
  // 选择字段
  const selectFields = fieldTypes.filter(field => 
    ['select', 'radio', 'checkbox'].includes(field.type)
  )
  
  // 日期时间字段
  const dateFields = fieldTypes.filter(field => 
    ['date', 'time'].includes(field.type)
  )
  
  // 特殊字段
  const specialFields = fieldTypes.filter(field => 
    ['email', 'phone'].includes(field.type)
  )

  const renderFieldGroup = (title, fields) => {
    if (fields.length === 0) return null
    
    return (
      <Card size="small" style={{ marginBottom: 16 }}>
        <Title level={5} style={{ marginBottom: 12 }}>
          {title}
        </Title>
        <Space direction="vertical" style={{ width: '100%' }}>
          {fields.map(fieldType => (
            <DraggableFieldItem
              key={fieldType.type}
              fieldType={fieldType}
              onAddField={onAddField}
            />
          ))}
        </Space>
      </Card>
    )
  }

  return (
    <div className="designer-sidebar">
      <div style={{ padding: 16 }}>
        <Title level={4} style={{ marginBottom: 16 }}>
          字段组件
        </Title>
        
        <Text type="secondary" style={{ fontSize: 12, display: 'block', marginBottom: 16 }}>
          拖拽字段到右侧画布中
        </Text>
        
        {renderFieldGroup('基础字段', basicFields)}
        {renderFieldGroup('选择字段', selectFields)}
        {renderFieldGroup('日期时间', dateFields)}
        {renderFieldGroup('特殊字段', specialFields)}
      </div>
    </div>
  )
}

export default FieldSidebar
