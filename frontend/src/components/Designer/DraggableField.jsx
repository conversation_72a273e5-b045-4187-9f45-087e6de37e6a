import React, { useRef } from 'react'
import { Button, Space, Popconfirm } from 'antd'
import { useDrag, useDrop } from 'react-dnd'
import { 
  CopyOutlined, 
  DeleteOutlined, 
  DragOutlined 
} from '@ant-design/icons'

function DraggableField({ 
  field, 
  index, 
  isSelected, 
  onClick, 
  onUpdate, 
  onDelete, 
  onMove, 
  children 
}) {
  const ref = useRef(null)

  const [{ handlerId }, drop] = useDrop({
    accept: 'FIELD_ITEM',
    collect(monitor) {
      return {
        handlerId: monitor.getHandlerId()
      }
    },
    hover(item, monitor) {
      if (!ref.current) {
        return
      }
      const dragIndex = item.index
      const hoverIndex = index

      // 不要替换自己
      if (dragIndex === hoverIndex) {
        return
      }

      // 确定矩形边界
      const hoverBoundingRect = ref.current?.getBoundingClientRect()

      // 获取垂直中点
      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2

      // 确定鼠标位置
      const clientOffset = monitor.getClientOffset()

      // 获取相对于悬停项顶部的像素
      const hoverClientY = clientOffset.y - hoverBoundingRect.top

      // 只在鼠标越过一半项目高度时执行移动
      // 向下拖动时，只有当光标低于50%时才移动
      // 向上拖动时，只有当光标高于50%时才移动

      // 向下拖动
      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        return
      }

      // 向上拖动
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        return
      }

      // 执行移动
      onMove(dragIndex, hoverIndex)

      // 注意：我们正在改变监视器中的项目！
      // 一般来说最好避免突变，但这里是为了性能
      // 避免昂贵的索引搜索。
      item.index = hoverIndex
    }
  })

  const [{ isDragging }, drag] = useDrag({
    type: 'FIELD_ITEM',
    item: () => {
      return { id: field.id, index }
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging()
    })
  })

  const opacity = isDragging ? 0.4 : 1
  drag(drop(ref))

  const handleCopy = (e) => {
    e.stopPropagation()
    const newField = {
      ...field,
      id: `field_${Date.now()}`,
      key: `${field.key}_copy`,
      label: `${field.label} 副本`
    }
    onUpdate(newField.id, newField)
  }

  const handleDelete = (e) => {
    e.stopPropagation()
    onDelete(field.id)
  }

  return (
    <div
      ref={ref}
      className={`field-item ${isSelected ? 'selected' : ''}`}
      style={{ opacity }}
      onClick={onClick}
      data-handler-id={handlerId}
    >
      {children}
      
      {/* 字段操作按钮 */}
      <div className="field-actions">
        <Space>
          <Button
            type="text"
            size="small"
            icon={<DragOutlined />}
            style={{ cursor: 'move' }}
            title="拖拽移动"
          />
          <Button
            type="text"
            size="small"
            icon={<CopyOutlined />}
            onClick={handleCopy}
            title="复制字段"
          />
          <Popconfirm
            title="确定要删除这个字段吗？"
            onConfirm={handleDelete}
            okText="确定"
            cancelText="取消"
            onClick={(e) => e.stopPropagation()}
          >
            <Button
              type="text"
              size="small"
              danger
              icon={<DeleteOutlined />}
              title="删除字段"
            />
          </Popconfirm>
        </Space>
      </div>
    </div>
  )
}

export default DraggableField
