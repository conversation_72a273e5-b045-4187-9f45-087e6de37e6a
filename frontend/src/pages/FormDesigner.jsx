import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { message } from 'antd'
import { formApi } from '../utils/api'

import FieldSidebar from '../components/Designer/FieldSidebar'
import DesignCanvas from '../components/Designer/DesignCanvas'
import PropertyPanel from '../components/Designer/PropertyPanel'
import DesignerToolbar from '../components/Designer/DesignerToolbar'

function FormDesigner() {
  const { id } = useParams()
  const navigate = useNavigate()
  const isEdit = !!id

  // 表单数据
  const [formData, setFormData] = useState({
    name: '未命名表单',
    description: '',
    status: 'draft',
    fields: [],
    settings: {
      submitText: '提交',
      resetText: '重置',
      layout: 'vertical'
    }
  })

  // 当前选中的字段
  const [selectedField, setSelectedField] = useState(null)
  
  // 字段类型列表
  const [fieldTypes, setFieldTypes] = useState([])
  
  // 加载状态
  const [loading, setLoading] = useState(false)

  // 获取字段类型
  const fetchFieldTypes = async () => {
    try {
      const response = await formApi.getFieldTypes()
      setFieldTypes(response.data || [])
    } catch (error) {
      console.error('获取字段类型失败:', error)
    }
  }

  // 获取表单数据（编辑模式）
  const fetchFormData = async () => {
    if (!id) return
    
    setLoading(true)
    try {
      const response = await formApi.getForm(id)
      setFormData(response.data)
    } catch (error) {
      console.error('获取表单数据失败:', error)
      message.error('获取表单数据失败')
    } finally {
      setLoading(false)
    }
  }

  // 保存表单
  const handleSave = async () => {
    if (!formData.name.trim()) {
      message.error('请输入表单名称')
      return
    }

    if (formData.fields.length === 0) {
      message.error('请至少添加一个字段')
      return
    }

    setLoading(true)
    try {
      let response
      if (isEdit) {
        response = await formApi.updateForm(id, formData)
        message.success('保存成功')
      } else {
        response = await formApi.createForm(formData)
        message.success('创建成功')
        // 创建成功后跳转到编辑页面
        navigate(`/designer/${response.data.id}`, { replace: true })
      }
    } catch (error) {
      console.error('保存表单失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 预览表单
  const handlePreview = () => {
    if (!id) {
      message.warning('请先保存表单')
      return
    }
    window.open(`/preview/${id}`, '_blank')
  }

  // 添加字段
  const handleAddField = (fieldType) => {
    const newField = {
      id: `field_${Date.now()}`,
      key: `field_${formData.fields.length + 1}`,
      type: fieldType.type,
      label: fieldType.defaultProps.label,
      ...fieldType.defaultProps
    }
    
    setFormData(prev => ({
      ...prev,
      fields: [...prev.fields, newField]
    }))
    
    setSelectedField(newField)
  }

  // 更新字段
  const handleUpdateField = (fieldId, updates) => {
    setFormData(prev => ({
      ...prev,
      fields: prev.fields.map(field => 
        field.id === fieldId ? { ...field, ...updates } : field
      )
    }))
    
    // 更新选中字段
    if (selectedField && selectedField.id === fieldId) {
      setSelectedField(prev => ({ ...prev, ...updates }))
    }
  }

  // 删除字段
  const handleDeleteField = (fieldId) => {
    setFormData(prev => ({
      ...prev,
      fields: prev.fields.filter(field => field.id !== fieldId)
    }))
    
    // 如果删除的是当前选中字段，清空选中状态
    if (selectedField && selectedField.id === fieldId) {
      setSelectedField(null)
    }
  }

  // 移动字段
  const handleMoveField = (dragIndex, hoverIndex) => {
    const dragField = formData.fields[dragIndex]
    const newFields = [...formData.fields]
    newFields.splice(dragIndex, 1)
    newFields.splice(hoverIndex, 0, dragField)
    
    setFormData(prev => ({
      ...prev,
      fields: newFields
    }))
  }

  // 更新表单设置
  const handleUpdateForm = (updates) => {
    setFormData(prev => ({ ...prev, ...updates }))
  }

  useEffect(() => {
    fetchFieldTypes()
    if (isEdit) {
      fetchFormData()
    }
  }, [id])

  return (
    <div className="form-designer">
      {/* 左侧字段面板 */}
      <FieldSidebar 
        fieldTypes={fieldTypes}
        onAddField={handleAddField}
      />
      
      {/* 中间设计画布 */}
      <div className="designer-canvas">
        <DesignerToolbar
          formData={formData}
          loading={loading}
          onSave={handleSave}
          onPreview={handlePreview}
          onUpdateForm={handleUpdateForm}
        />
        
        <DesignCanvas
          fields={formData.fields}
          selectedField={selectedField}
          onSelectField={setSelectedField}
          onUpdateField={handleUpdateField}
          onDeleteField={handleDeleteField}
          onMoveField={handleMoveField}
          onAddField={handleAddField}
        />
      </div>
      
      {/* 右侧属性面板 */}
      <PropertyPanel
        selectedField={selectedField}
        formData={formData}
        onUpdateField={handleUpdateField}
        onUpdateForm={handleUpdateForm}
      />
    </div>
  )
}

export default FormDesigner
