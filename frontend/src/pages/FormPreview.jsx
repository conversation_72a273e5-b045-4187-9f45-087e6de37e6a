import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { 
  Card, 
  Form, 
  Button, 
  Space, 
  message, 
  Typography, 
  Spin,
  Result
} from 'antd'
import { ArrowLeftOutlined, EditOutlined } from '@ant-design/icons'
import { formApi } from '../utils/api'
import FieldRenderer from '../components/Form/FieldRenderer'

const { Title, Paragraph } = Typography

function FormPreview() {
  const { id } = useParams()
  const navigate = useNavigate()
  const [form] = Form.useForm()
  
  const [formData, setFormData] = useState(null)
  const [loading, setLoading] = useState(false)
  const [submitting, setSubmitting] = useState(false)

  // 获取表单数据
  const fetchFormData = async () => {
    setLoading(true)
    try {
      const response = await formApi.getForm(id)
      setFormData(response.data)
    } catch (error) {
      console.error('获取表单数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 提交表单
  const handleSubmit = async (values) => {
    setSubmitting(true)
    try {
      await formApi.submitFormData(id, values)
      message.success('提交成功！')
      form.resetFields()
    } catch (error) {
      console.error('提交失败:', error)
    } finally {
      setSubmitting(false)
    }
  }

  // 重置表单
  const handleReset = () => {
    form.resetFields()
    message.info('表单已重置')
  }

  useEffect(() => {
    if (id) {
      fetchFormData()
    }
  }, [id])

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '100px 0' }}>
        <Spin size="large" />
      </div>
    )
  }

  if (!formData) {
    return (
      <Result
        status="404"
        title="表单不存在"
        subTitle="抱歉，您访问的表单不存在或已被删除。"
        extra={
          <Button type="primary" onClick={() => navigate('/')}>
            返回首页
          </Button>
        }
      />
    )
  }

  return (
    <div style={{ maxWidth: 800, margin: '0 auto' }}>
      {/* 页面头部 */}
      <Card style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <div style={{ flex: 1 }}>
            <Title level={2} style={{ marginBottom: 8 }}>
              {formData.name}
            </Title>
            {formData.description && (
              <Paragraph type="secondary" style={{ fontSize: 16 }}>
                {formData.description}
              </Paragraph>
            )}
          </div>
          
          <Space>
            <Button 
              icon={<ArrowLeftOutlined />}
              onClick={() => navigate('/')}
            >
              返回
            </Button>
            <Button 
              type="primary"
              icon={<EditOutlined />}
              onClick={() => navigate(`/designer/${id}`)}
            >
              编辑表单
            </Button>
          </Space>
        </div>
      </Card>

      {/* 表单内容 */}
      <Card>
        <Form
          form={form}
          layout={formData.settings?.layout || 'vertical'}
          onFinish={handleSubmit}
          size="large"
        >
          {formData.fields?.map(field => (
            <FieldRenderer
              key={field.id}
              field={field}
            />
          ))}

          {formData.fields?.length > 0 && (
            <Form.Item style={{ marginTop: 32 }}>
              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={submitting}
                  size="large"
                >
                  {formData.settings?.submitText || '提交'}
                </Button>
                <Button
                  onClick={handleReset}
                  size="large"
                >
                  {formData.settings?.resetText || '重置'}
                </Button>
              </Space>
            </Form.Item>
          )}
        </Form>

        {formData.fields?.length === 0 && (
          <div style={{ textAlign: 'center', padding: '60px 0', color: '#999' }}>
            <Paragraph>
              该表单暂无字段，请先添加字段后再预览。
            </Paragraph>
            <Button 
              type="primary"
              onClick={() => navigate(`/designer/${id}`)}
            >
              去添加字段
            </Button>
          </div>
        )}
      </Card>
    </div>
  )
}

export default FormPreview
