import React, { useState, useEffect } from 'react'
import { 
  Card, 
  Button, 
  Table, 
  Space, 
  Tag, 
  Popconfirm, 
  message,
  Typography,
  Row,
  Col
} from 'antd'
import { 
  EditOutlined, 
  EyeOutlined, 
  DeleteOutlined, 
  BarChartOutlined,
  PlusOutlined 
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { formApi } from '../utils/api'

const { Title, Paragraph } = Typography

function FormList() {
  const [forms, setForms] = useState([])
  const [loading, setLoading] = useState(false)
  const navigate = useNavigate()

  // 获取表单列表
  const fetchForms = async () => {
    setLoading(true)
    try {
      const response = await formApi.getForms()
      setForms(response.data || [])
    } catch (error) {
      console.error('获取表单列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 删除表单
  const handleDelete = async (id) => {
    try {
      await formApi.deleteForm(id)
      message.success('删除成功')
      fetchForms()
    } catch (error) {
      console.error('删除表单失败:', error)
    }
  }

  // 状态标签渲染
  const renderStatus = (status) => {
    const statusMap = {
      draft: { color: 'default', text: '草稿' },
      published: { color: 'success', text: '已发布' },
      archived: { color: 'warning', text: '已归档' }
    }
    const config = statusMap[status] || statusMap.draft
    return <Tag color={config.color}>{config.text}</Tag>
  }

  // 表格列配置
  const columns = [
    {
      title: '表单名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 500, marginBottom: 4 }}>{text}</div>
          {record.description && (
            <div style={{ color: '#666', fontSize: 12 }}>
              {record.description}
            </div>
          )}
        </div>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: renderStatus
    },
    {
      title: '字段数量',
      dataIndex: 'fields',
      key: 'fieldCount',
      width: 100,
      render: (fields) => fields?.length || 0
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      render: (time) => new Date(time).toLocaleString()
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => navigate(`/designer/${record.id}`)}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => navigate(`/preview/${record.id}`)}
          >
            预览
          </Button>
          <Button
            type="link"
            size="small"
            icon={<BarChartOutlined />}
            onClick={() => navigate(`/data/${record.id}`)}
          >
            数据
          </Button>
          <Popconfirm
            title="确定要删除这个表单吗？"
            description="删除后将无法恢复，包括所有提交的数据。"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ]

  useEffect(() => {
    fetchForms()
  }, [])

  return (
    <div>
      {/* 页面头部 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Card>
            <div style={{ textAlign: 'center', padding: '40px 0' }}>
              <Title level={2}>轻搭动态表单平台</Title>
              <Paragraph style={{ fontSize: 16, color: '#666', marginBottom: 24 }}>
                简单易用的可视化表单构建工具，支持拖拽设计、实时预览、数据管理
              </Paragraph>
              <Button 
                type="primary" 
                size="large"
                icon={<PlusOutlined />}
                onClick={() => navigate('/designer')}
              >
                创建第一个表单
              </Button>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 表单列表 */}
      <Card 
        title="我的表单" 
        extra={
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => navigate('/designer')}
          >
            新建表单
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={forms}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个表单`
          }}
          locale={{
            emptyText: '暂无表单，点击上方按钮创建第一个表单'
          }}
        />
      </Card>
    </div>
  )
}

export default FormList
