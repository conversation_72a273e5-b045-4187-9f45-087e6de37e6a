import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Typography, 
  Spin,
  Result,
  Tag,
  Modal,
  Descriptions
} from 'antd'
import { 
  ArrowLeftOutlined, 
  EditOutlined, 
  EyeOutlined,
  DownloadOutlined 
} from '@ant-design/icons'
import { formApi } from '../utils/api'

const { Title, Paragraph } = Typography

function FormData() {
  const { id } = useParams()
  const navigate = useNavigate()
  
  const [formData, setFormData] = useState(null)
  const [submissions, setSubmissions] = useState([])
  const [loading, setLoading] = useState(false)
  const [selectedSubmission, setSelectedSubmission] = useState(null)
  const [detailVisible, setDetailVisible] = useState(false)

  // 获取表单信息
  const fetchFormData = async () => {
    try {
      const response = await formApi.getForm(id)
      setFormData(response.data)
    } catch (error) {
      console.error('获取表单数据失败:', error)
    }
  }

  // 获取提交数据
  const fetchSubmissions = async () => {
    setLoading(true)
    try {
      const response = await formApi.getFormData(id)
      setSubmissions(response.data || [])
    } catch (error) {
      console.error('获取提交数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 查看详情
  const handleViewDetail = (submission) => {
    setSelectedSubmission(submission)
    setDetailVisible(true)
  }

  // 导出数据
  const handleExport = () => {
    if (submissions.length === 0) {
      return
    }

    // 简单的CSV导出
    const headers = formData.fields.map(field => field.label)
    const csvContent = [
      headers.join(','),
      ...submissions.map(submission => 
        formData.fields.map(field => {
          const value = submission.data[field.key] || ''
          return `"${value}"`
        }).join(',')
      )
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `${formData.name}_数据.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // 生成表格列
  const generateColumns = () => {
    if (!formData || !formData.fields) return []

    const fieldColumns = formData.fields.slice(0, 4).map(field => ({
      title: field.label,
      dataIndex: ['data', field.key],
      key: field.key,
      ellipsis: true,
      render: (value) => {
        if (Array.isArray(value)) {
          return value.join(', ')
        }
        return value || '-'
      }
    }))

    return [
      ...fieldColumns,
      {
        title: '提交时间',
        dataIndex: 'submittedAt',
        key: 'submittedAt',
        width: 180,
        render: (time) => new Date(time).toLocaleString()
      },
      {
        title: '操作',
        key: 'actions',
        width: 120,
        render: (_, record) => (
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            查看详情
          </Button>
        )
      }
    ]
  }

  useEffect(() => {
    if (id) {
      fetchFormData()
      fetchSubmissions()
    }
  }, [id])

  if (!formData) {
    return (
      <div style={{ textAlign: 'center', padding: '100px 0' }}>
        <Spin size="large" />
      </div>
    )
  }

  return (
    <div>
      {/* 页面头部 */}
      <Card style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <div style={{ flex: 1 }}>
            <Title level={2} style={{ marginBottom: 8 }}>
              {formData.name} - 数据管理
            </Title>
            <Paragraph type="secondary">
              共收到 {submissions.length} 条提交数据
            </Paragraph>
          </div>
          
          <Space>
            <Button 
              icon={<ArrowLeftOutlined />}
              onClick={() => navigate('/')}
            >
              返回
            </Button>
            <Button 
              icon={<EditOutlined />}
              onClick={() => navigate(`/designer/${id}`)}
            >
              编辑表单
            </Button>
            <Button 
              type="primary"
              icon={<DownloadOutlined />}
              onClick={handleExport}
              disabled={submissions.length === 0}
            >
              导出数据
            </Button>
          </Space>
        </div>
      </Card>

      {/* 数据表格 */}
      <Card>
        <Table
          columns={generateColumns()}
          dataSource={submissions}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条数据`
          }}
          locale={{
            emptyText: '暂无提交数据'
          }}
        />
      </Card>

      {/* 详情弹窗 */}
      <Modal
        title="提交详情"
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailVisible(false)}>
            关闭
          </Button>
        ]}
        width={600}
      >
        {selectedSubmission && (
          <Descriptions column={1} bordered>
            <Descriptions.Item label="提交时间">
              {new Date(selectedSubmission.submittedAt).toLocaleString()}
            </Descriptions.Item>
            {formData.fields.map(field => (
              <Descriptions.Item key={field.key} label={field.label}>
                {(() => {
                  const value = selectedSubmission.data[field.key]
                  if (Array.isArray(value)) {
                    return value.map(v => <Tag key={v}>{v}</Tag>)
                  }
                  return value || '-'
                })()}
              </Descriptions.Item>
            ))}
          </Descriptions>
        )}
      </Modal>
    </div>
  )
}

export default FormData
