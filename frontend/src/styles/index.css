/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

#root {
  min-height: 100vh;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 布局样式 */
.app-layout {
  min-height: 100vh;
}

.app-header {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.app-logo {
  display: flex;
  align-items: center;
  font-size: 20px;
  font-weight: bold;
  color: #1890ff;
}

.app-content {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

/* 表单设计器样式 */
.form-designer {
  display: flex;
  height: calc(100vh - 112px);
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.designer-sidebar {
  width: 280px;
  border-right: 1px solid #f0f0f0;
  background: #fafafa;
}

.designer-canvas {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.designer-toolbar {
  height: 64px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
}

.designer-preview {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  background: #f8f9fa;
}

.designer-properties {
  width: 320px;
  border-left: 1px solid #f0f0f0;
  background: #fafafa;
}

/* 字段组件样式 */
.field-item {
  margin-bottom: 16px;
  padding: 12px;
  border: 2px dashed transparent;
  border-radius: 6px;
  transition: all 0.2s;
  position: relative;
}

.field-item:hover {
  border-color: #1890ff;
  background: rgba(24, 144, 255, 0.02);
}

.field-item.selected {
  border-color: #1890ff;
  background: rgba(24, 144, 255, 0.05);
}

.field-item.dragging {
  transform: rotate(5deg);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  z-index: 1000;
}

.field-item .field-actions {
  position: absolute;
  top: -8px;
  right: -8px;
  display: none;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.field-item:hover .field-actions,
.field-item.selected .field-actions {
  display: block;
}

/* 拖拽样式 */
.drag-item {
  cursor: move;
  padding: 8px 12px;
  margin: 4px 0;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  transition: all 0.2s;
}

.drag-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

.drop-zone {
  min-height: 200px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  transition: all 0.2s;
}

.drop-zone.drag-over {
  border-color: #1890ff;
  background: rgba(24, 144, 255, 0.02);
  color: #1890ff;
}

/* 工具栏按钮样式 */
.designer-toolbar .ant-btn {
  transition: all 0.2s ease;
  font-weight: 500;
}

.designer-toolbar .ant-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.designer-toolbar .ant-btn-primary:hover {
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

/* 状态选择器样式 */
.status-selector {
  transition: all 0.2s ease;
}

.status-selector:hover {
  border-color: #1890ff !important;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2) !important;
  transform: translateY(-1px);
}

.status-dropdown .ant-dropdown-menu {
  border-radius: 10px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  padding: 6px;
  border: 1px solid #f0f0f0;
}

.status-dropdown .ant-dropdown-menu-item {
  border-radius: 8px;
  margin: 2px 0;
  padding: 8px 12px;
}

.status-dropdown .ant-dropdown-menu-item:hover {
  background-color: #f8f9fa;
}

/* 表单名称输入框样式 */
.designer-toolbar .ant-input {
  transition: all 0.2s ease;
}

.designer-toolbar .ant-input:hover {
  background-color: rgba(24, 144, 255, 0.02);
  border-radius: 6px;
}

.designer-toolbar .ant-input:focus {
  background-color: rgba(24, 144, 255, 0.04);
  border-radius: 6px;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

/* 响应式 */
@media (max-width: 1200px) {
  .designer-properties {
    width: 280px;
  }
}

@media (max-width: 768px) {
  .form-designer {
    flex-direction: column;
    height: auto;
  }

  .designer-sidebar,
  .designer-properties {
    width: 100%;
    border: none;
    border-bottom: 1px solid #f0f0f0;
  }
}
