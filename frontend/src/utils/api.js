import axios from 'axios'
import { message } from 'antd'

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加token等认证信息
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    const { data } = response
    
    // 如果后端返回的数据结构包含success字段
    if (data.success === false) {
      message.error(data.message || '请求失败')
      return Promise.reject(new Error(data.message || '请求失败'))
    }
    
    return data
  },
  (error) => {
    console.error('API Error:', error)
    
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 400:
          message.error(data.message || '请求参数错误')
          break
        case 401:
          message.error('未授权，请重新登录')
          break
        case 403:
          message.error('拒绝访问')
          break
        case 404:
          message.error('请求的资源不存在')
          break
        case 500:
          message.error('服务器内部错误')
          break
        default:
          message.error(data.message || '请求失败')
      }
    } else if (error.request) {
      message.error('网络错误，请检查网络连接')
    } else {
      message.error('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)

// API方法
export const formApi = {
  // 获取表单列表
  getForms: () => api.get('/forms'),
  
  // 获取表单详情
  getForm: (id) => api.get(`/forms/${id}`),
  
  // 创建表单
  createForm: (data) => api.post('/forms', data),
  
  // 更新表单
  updateForm: (id, data) => api.put(`/forms/${id}`, data),
  
  // 删除表单
  deleteForm: (id) => api.delete(`/forms/${id}`),
  
  // 获取字段类型
  getFieldTypes: () => api.get('/fields/types'),
  
  // 提交表单数据
  submitFormData: (formId, data) => api.post(`/data/${formId}`, data),
  
  // 获取表单提交数据
  getFormData: (formId) => api.get(`/data/${formId}`)
}

export default api
