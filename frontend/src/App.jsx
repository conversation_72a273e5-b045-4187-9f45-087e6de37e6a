import React from 'react'
import { Routes, Route } from 'react-router-dom'
import { Layout } from 'antd'
import { DndProvider } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'

import Header from './components/Layout/Header'
import FormList from './pages/FormList'
import FormDesigner from './pages/FormDesigner'
import FormPreview from './pages/FormPreview'
import FormData from './pages/FormData'

const { Content } = Layout

function App() {
  return (
    <DndProvider backend={HTML5Backend}>
      <Layout className="app-layout">
        <Header />
        <Content className="app-content">
          <Routes>
            <Route path="/" element={<FormList />} />
            <Route path="/designer/:id?" element={<FormDesigner />} />
            <Route path="/preview/:id" element={<FormPreview />} />
            <Route path="/data/:id" element={<FormData />} />
          </Routes>
        </Content>
      </Layout>
    </DndProvider>
  )
}

export default App
