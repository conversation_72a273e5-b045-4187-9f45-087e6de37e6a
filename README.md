# 轻搭 - 动态表单平台

一个简单易用的动态表单构建平台，支持可视化表单设计和自定义字段配置。

## 项目结构

```
qingda-form-platform/
├── frontend/          # 前端项目 (React + Vite)
│   ├── src/
│   │   ├── components/    # 通用组件
│   │   ├── pages/        # 页面组件
│   │   ├── hooks/        # 自定义hooks
│   │   ├── utils/        # 工具函数
│   │   └── styles/       # 样式文件
│   └── package.json
├── backend/           # 后端项目 (Express.js)
│   ├── src/
│   │   ├── routes/       # 路由
│   │   ├── controllers/  # 控制器
│   │   ├── models/       # 数据模型
│   │   ├── middleware/   # 中间件
│   │   └── utils/        # 工具函数
│   └── package.json
└── package.json       # 根目录配置

```

## 功能特性

- 🎨 可视化表单设计器
- 🔧 丰富的字段类型支持
- 📱 响应式设计
- 💾 表单数据管理
- 🚀 简单易用的API

## 快速开始

### 安装依赖
```bash
npm run install:all
```

### 启动开发环境
```bash
npm run dev
```

### 访问地址
- 前端: http://localhost:5173
- 后端API: http://localhost:3001

## 技术栈

### 前端
- React 18
- Vite
- Ant Design
- React DnD (拖拽功能)
- Axios

### 后端
- Node.js
- Express.js
- CORS
- 内存数据存储 (开发阶段)
