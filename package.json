{"name": "qingda-form-platform", "version": "1.0.0", "description": "轻搭 - 动态表单平台", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install", "build": "cd frontend && npm run build", "start": "cd backend && npm start"}, "keywords": ["form", "dynamic", "platform", "builder"], "author": "轻搭团队", "license": "MIT", "devDependencies": {"concurrently": "^8.2.0"}}